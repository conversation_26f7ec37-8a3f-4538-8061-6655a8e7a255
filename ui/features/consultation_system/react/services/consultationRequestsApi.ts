import doFetchApi from '@canvas/do-fetch-api-effect'
import type {
  ConsultationRequest,
  ConsultationRequestFormData,
  ConsultationRequestResponse,
  FacultyDashboardData,
  StudentFormData,
  ConsultationFilters
} from '../types'

const API_BASE = '/consultation_requests'

// API Response wrapper types
interface ApiResult<T> {
  data?: T
  hasError: boolean
  errorMessage?: string
}

export const fetchConsultationRequests = async (filters?: ConsultationFilters): Promise<ApiResult<ConsultationRequestResponse>> => {
  try {
    const { json } = await doFetchApi({
      path: API_BASE,
      method: 'GET',
      params: filters as any
    })
    return {
      data: json as ConsultationRequestResponse,
      hasError: false
    }
  } catch (error: any) {
    return {
      hasError: true,
      errorMessage: error.message || 'Failed to fetch consultation requests'
    }
  }
}

export const fetchConsultationRequest = async (id: string): Promise<ApiResult<ConsultationRequest>> => {
  try {
    const { json } = await doFetchApi({
      path: `${API_BASE}/${id}`,
      method: 'GET'
    })
    return {
      data: json as ConsultationRequest,
      hasError: false
    }
  } catch (error: any) {
    return {
      hasError: true,
      errorMessage: error.message || 'Failed to fetch consultation request'
    }
  }
}

export const createConsultationRequest = async (data: ConsultationRequestFormData): Promise<ApiResult<ConsultationRequest>> => {
  try {
    const { json } = await doFetchApi({
      path: API_BASE,
      method: 'POST',
      body: {
        consultation_request: data
      }
    })
    return {
      data: json as ConsultationRequest,
      hasError: false
    }
  } catch (error: any) {
    // Handle FetchApiError with response body
    let errorMessage = 'Failed to create consultation request'

    if (error.response) {
      try {
        const errorData = await error.response.json()
        if (errorData.errors && Array.isArray(errorData.errors)) {
          errorMessage = errorData.errors.join(', ')
        } else if (errorData.error) {
          errorMessage = errorData.error
        }
      } catch (parseError) {
        // If we can't parse the response, fall back to the original error message
        errorMessage = error.message || errorMessage
      }
    } else {
      errorMessage = error.message || errorMessage
    }

    return {
      hasError: true,
      errorMessage
    }
  }
}

export const updateConsultationRequest = async (id: string, data: Partial<ConsultationRequestFormData>): Promise<ApiResult<ConsultationRequest>> => {
  try {
    const { json } = await doFetchApi({
      path: `${API_BASE}/${id}`,
      method: 'PATCH',
      body: {
        consultation_request: data
      }
    })
    return {
      data: json as ConsultationRequest,
      hasError: false
    }
  } catch (error: any) {
    // Handle FetchApiError with response body
    let errorMessage = 'Failed to update consultation request'

    if (error.response) {
      try {
        const errorData = await error.response.json()
        if (errorData.errors && Array.isArray(errorData.errors)) {
          errorMessage = errorData.errors.join(', ')
        } else if (errorData.error) {
          errorMessage = errorData.error
        }
      } catch (parseError) {
        // If we can't parse the response, fall back to the original error message
        errorMessage = error.message || errorMessage
      }
    } else {
      errorMessage = error.message || errorMessage
    }

    return {
      hasError: true,
      errorMessage
    }
  }
}

export const cancelConsultationRequest = async (id: string): Promise<ApiResult<void>> => {
  try {
    await doFetchApi({
      path: `${API_BASE}/${id}`,
      method: 'DELETE'
    })
    return {
      hasError: false
    }
  } catch (error: any) {
    let errorMessage = 'Failed to cancel consultation request'

    if (error.response) {
      try {
        const errorData = await error.response.json()
        if (errorData.errors && Array.isArray(errorData.errors)) {
          errorMessage = errorData.errors.join(', ')
        } else if (errorData.error) {
          errorMessage = errorData.error
        }
      } catch (parseError) {
        // If we can't parse the response, fall back to the original error message
        errorMessage = error.message || errorMessage
      }
    } else {
      errorMessage = error.message || errorMessage
    }

    return {
      hasError: true,
      errorMessage
    }
  }
}

export const approveConsultationRequest = async (id: string, comment?: string): Promise<ApiResult<ConsultationRequest>> => {
  try {
    const { json } = await doFetchApi({
      path: `${API_BASE}/${id}/approve`,
      method: 'POST',
      body: {
        comment: comment
      }
    })
    return {
      data: json as ConsultationRequest,
      hasError: false
    }
  } catch (error: any) {
    // Handle FetchApiError with response body
    let errorMessage = 'Failed to approve consultation request'

    if (error.response) {
      try {
        const errorData = await error.response.json()
        if (errorData.errors && Array.isArray(errorData.errors)) {
          errorMessage = errorData.errors.join(', ')
        } else if (errorData.error) {
          errorMessage = errorData.error
        }
      } catch (parseError) {
        // If we can't parse the response, fall back to the original error message
        errorMessage = error.message || errorMessage
      }
    } else {
      errorMessage = error.message || errorMessage
    }

    return {
      hasError: true,
      errorMessage
    }
  }
}

export const declineConsultationRequest = async (id: string, comment: string): Promise<ApiResult<ConsultationRequest>> => {
  try {
    const { json } = await doFetchApi({
      path: `${API_BASE}/${id}/decline`,
      method: 'POST',
      body: {
        comment: comment
      }
    })
    return {
      data: json as ConsultationRequest,
      hasError: false
    }
  } catch (error: any) {
    // Handle FetchApiError with response body
    let errorMessage = 'Failed to decline consultation request'

    if (error.response) {
      try {
        const errorData = await error.response.json()
        if (errorData.errors && Array.isArray(errorData.errors)) {
          errorMessage = errorData.errors.join(', ')
        } else if (errorData.error) {
          errorMessage = errorData.error
        }
      } catch (parseError) {
        // If we can't parse the response, fall back to the original error message
        errorMessage = error.message || errorMessage
      }
    } else {
      errorMessage = error.message || errorMessage
    }

    return {
      hasError: true,
      errorMessage
    }
  }
}

export const completeConsultationRequest = async (id: string, completionNotes?: string): Promise<ApiResult<ConsultationRequest>> => {
  try {
    const { json } = await doFetchApi({
      path: `${API_BASE}/${id}/complete`,
      method: 'POST',
      body: {
        completion_notes: completionNotes
      }
    })
    return {
      data: json as ConsultationRequest,
      hasError: false
    }
  } catch (error: any) {
    // Handle FetchApiError with response body
    let errorMessage = 'Failed to complete consultation request'

    if (error.response) {
      try {
        const errorData = await error.response.json()
        if (errorData.errors && Array.isArray(errorData.errors)) {
          errorMessage = errorData.errors.join(', ')
        } else if (errorData.error) {
          errorMessage = errorData.error
        }
      } catch (parseError) {
        // If we can't parse the response, fall back to the original error message
        errorMessage = error.message || errorMessage
      }
    } else {
      errorMessage = error.message || errorMessage
    }

    return {
      hasError: true,
      errorMessage
    }
  }
}

export const fetchFacultyDashboard = async (): Promise<ApiResult<FacultyDashboardData>> => {
  try {
    const { json } = await doFetchApi({
      path: `${API_BASE}/faculty_dashboard`,
      method: 'GET'
    })
    return {
      data: json as FacultyDashboardData,
      hasError: false
    }
  } catch (error: any) {
    return {
      hasError: true,
      errorMessage: error.message || 'Failed to fetch faculty dashboard data'
    }
  }
}

export const fetchStudentFormData = async (): Promise<ApiResult<StudentFormData>> => {
  try {
    const { json } = await doFetchApi({
      path: `${API_BASE}/student_form`,
      method: 'GET'
    })
    return {
      data: json as StudentFormData,
      hasError: false
    }
  } catch (error: any) {
    return {
      hasError: true,
      errorMessage: error.message || 'Failed to fetch student form data'
    }
  }
}
