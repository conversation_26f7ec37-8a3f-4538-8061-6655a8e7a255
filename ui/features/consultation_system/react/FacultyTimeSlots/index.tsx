import React, { useState, useEffect } from 'react'
import { View } from '@instructure/ui-view'
import { Heading } from '@instructure/ui-heading'
import { Button } from '@instructure/ui-buttons'
import { Alert } from '@instructure/ui-alerts'
import { Spinner } from '@instructure/ui-spinner'
import { IconAddLine, IconClockLine, IconCalendarMonthLine, IconBulletListLine } from '@instructure/ui-icons'
import TimeSlotList from './TimeSlotList'
import TimeSlotCalendar from './TimeSlotCalendar'
import TimeSlotForm from './TimeSlotForm'
import { fetchTimeSlots, createTimeSlot, updateTimeSlot, deleteTimeSlot } from '../services/facultyTimeSlotsApi'
import type { FacultyTimeSlot, TimeSlotFormData } from '../types'

interface FacultyTimeSlotsProps {
  currentUserId: string
  initialTimeSlots?: FacultyTimeSlot[]
  daysOfWeek: string[]
}

const FacultyTimeSlots: React.FC<FacultyTimeSlotsProps> = ({
  currentUserId,
  initialTimeSlots = [],
  daysOfWeek
}) => {
  const [timeSlots, setTimeSlots] = useState<FacultyTimeSlot[]>(initialTimeSlots)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [showForm, setShowForm] = useState(false)
  const [editingSlot, setEditingSlot] = useState<FacultyTimeSlot | null>(null)
  const [viewMode, setViewMode] = useState<'calendar' | 'list'>('calendar')

  useEffect(() => {
    if (initialTimeSlots.length === 0) {
      loadTimeSlots()
    }
  }, [])

  const loadTimeSlots = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await fetchTimeSlots()
      setTimeSlots(response.time_slots)
    } catch (err) {
      setError('Failed to load time slots. Please try again.')
      console.error('Error loading time slots:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleCreateSlot = async (formData: TimeSlotFormData) => {
    try {
      setLoading(true)
      setError(null)
      const newSlot = await createTimeSlot(formData)
      setTimeSlots(prev => [...prev, newSlot])
      setShowForm(false)
      setSuccess('Time slot created successfully!')
      setTimeout(() => setSuccess(null), 5000)
    } catch (err: any) {
      setError(err.message || 'Failed to create time slot. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleUpdateSlot = async (id: string, formData: TimeSlotFormData) => {
    try {
      setLoading(true)
      setError(null)
      const updatedSlot = await updateTimeSlot(id, formData)
      setTimeSlots(prev => prev.map(slot => slot.id === id ? updatedSlot : slot))
      setEditingSlot(null)
      setSuccess('Time slot updated successfully!')
      setTimeout(() => setSuccess(null), 5000)
    } catch (err: any) {
      setError(err.message || 'Failed to update time slot. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteSlot = async (id: string) => {
    if (!confirm('Are you sure you want to delete this time slot? This action cannot be undone.')) {
      return
    }

    try {
      setLoading(true)
      setError(null)
      await deleteTimeSlot(id)
      setTimeSlots(prev => prev.filter(slot => slot.id !== id))
      setSuccess('Time slot deleted successfully!')
      setTimeout(() => setSuccess(null), 5000)
    } catch (err: any) {
      setError(err.message || 'Failed to delete time slot. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleEditSlot = (slot: FacultyTimeSlot) => {
    setEditingSlot(slot)
    setShowForm(true)
  }

  const handleCancelForm = () => {
    setShowForm(false)
    setEditingSlot(null)
  }

  const clearMessages = () => {
    setError(null)
    setSuccess(null)
  }

  return (
    <div className="consultation-system">
      <View as="div" padding="large">
        <div className="page-header">
          <Heading level="h1" margin="0 0 small 0">
            <IconClockLine /> Manage Consultation Time Slots
          </Heading>
          <p>Define your available time slots for student consultations. Students will be able to request appointments during these times.</p>
        </div>

        {error && (
          <Alert variant="error" margin="0 0 medium 0" onDismiss={clearMessages}>
            {error}
          </Alert>
        )}

        {success && (
          <Alert variant="success" margin="0 0 medium 0" onDismiss={clearMessages}>
            {success}
          </Alert>
        )}

        <View as="div" margin="0 0 large 0">
          <View as="div" display="flex" justifyItems="space-between" alignItems="center">
            <View as="div">
              <Button
                color="primary"
                renderIcon={IconAddLine}
                onClick={() => setShowForm(true)}
                disabled={loading}
              >
                Add New Time Slot
              </Button>
            </View>

            <View as="div" display="flex" gap="small">
              <Button
                color={viewMode === 'calendar' ? 'primary' : 'secondary'}
                renderIcon={IconCalendarMonthLine}
                onClick={() => setViewMode('calendar')}
                size="small"
              >
                Calendar View
              </Button>
              <Button
                color={viewMode === 'list' ? 'primary' : 'secondary'}
                renderIcon={IconBulletListLine}
                onClick={() => setViewMode('list')}
                size="small"
              >
                List View
              </Button>
            </View>
          </View>
        </View>

        {showForm && (
          <View as="div" margin="0 0 large 0">
            <TimeSlotForm
              daysOfWeek={daysOfWeek}
              initialData={editingSlot}
              onSubmit={editingSlot ? 
                (data) => handleUpdateSlot(editingSlot.id, data) : 
                handleCreateSlot
              }
              onCancel={handleCancelForm}
              loading={loading}
            />
          </View>
        )}

        {loading && !showForm ? (
          <View as="div" textAlign="center" padding="large">
            <Spinner renderTitle="Loading time slots..." />
          </View>
        ) : (
          <>
            {viewMode === 'calendar' ? (
              <TimeSlotCalendar
                currentUserId={currentUserId}
                onEdit={handleEditSlot}
                onDelete={handleDeleteSlot}
                loading={loading}
              />
            ) : (
              <TimeSlotList
                timeSlots={timeSlots}
                onEdit={handleEditSlot}
                onDelete={handleDeleteSlot}
                loading={loading}
              />
            )}
          </>
        )}

        {!loading && timeSlots.length === 0 && !showForm && (
          <View as="div" textAlign="center" padding="x-large">
            <div className="empty-state">
              <div className="empty-icon">
                <IconClockLine size="large" />
              </div>
              <Heading level="h3" margin="0 0 small 0">
                No Time Slots Defined
              </Heading>
              <p>You haven't created any consultation time slots yet. Add your first time slot to start accepting consultation requests from students.</p>
              <Button
                color="primary"
                renderIcon={IconAddLine}
                onClick={() => setShowForm(true)}
              >
                Create Your First Time Slot
              </Button>
            </div>
          </View>
        )}
      </View>
    </div>
  )
}

export default FacultyTimeSlots
